/**
 * 视觉脚本网络协议节点
 * 提供不同网络协议的支持，如UDP、TCP、HTTP等
 */
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NetworkSystem } from '../../network/NetworkSystem';

/**
 * UDP发送节点
 * 使用UDP协议发送数据
 */
export class UDPSendNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'address',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '目标端口',
      defaultValue: 8080
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的数据'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '发送失败'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const address = this.getInputValue('address') as string;
    const port = this.getInputValue('port') as number;
    const data = this.getInputValue('data');

    // 检查输入值是否有效
    if (!address || !port || data === undefined) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用网络系统发送消息（模拟UDP发送）
      // 在实际的UDP实现中，这里应该使用专门的UDP协议
      await networkSystem.sendToAll('udp_message', {
        address,
        port,
        data,
        timestamp: Date.now()
      });

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('UDP发送失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * HTTP请求节点
 * 发送HTTP请求
 */
export class HTTPRequestNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://example.com'
    });

    this.addInput({
      name: 'method',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求方法',
      defaultValue: 'GET'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '请求体',
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '请求失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const url = this.getInputValue('url') as string;
    const method = this.getInputValue('method') as string;
    const headers = this.getInputValue('headers') as Record<string, string>;
    const body = this.getInputValue('body');
    const timeout = this.getInputValue('timeout') as number;

    // 检查输入值是否有效
    if (!url) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取网络系统
    const networkSystem = this.context.world.getSystem(NetworkSystem);
    if (!networkSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用fetch API发送HTTP请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const fetchOptions: RequestInit = {
        method,
        headers,
        signal: controller.signal
      };

      // 添加请求体
      if (body !== undefined) {
        fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);

      // 获取响应数据
      const responseData = await response.text();
      let parsedData: any;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      // 设置输出值
      this.setOutputValue('response', parsedData);
      this.setOutputValue('statusCode', response.status);
      this.setOutputValue('headers', Object.fromEntries(response.headers.entries()));

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('HTTP请求失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * WebSocket连接节点
 * 创建WebSocket连接
 */
export class WebSocketConnectNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'protocols',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '协议列表',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebSocket连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const url = this.getInputValue('url') as string;
    const protocols = this.getInputValue('protocols') as string[];

    // 检查输入值是否有效
    if (!url) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建WebSocket连接
      const ws = new WebSocket(url, protocols);

      // 等待连接建立
      await new Promise((resolve, reject) => {
        ws.onopen = () => resolve(ws);
        ws.onerror = (error) => reject(error);

        // 设置超时
        setTimeout(() => reject(new Error('连接超时')), 5000);
      });

      // 设置输出值
      this.setOutputValue('connection', ws);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * TCP连接节点
 * 创建TCP连接（在浏览器环境中模拟）
 */
export class TCPConnectNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'host',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主机地址',
      defaultValue: 'localhost'
    });

    this.addInput({
      name: 'port',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '端口号',
      defaultValue: 8080
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'TCP连接'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const host = this.getInputValue('host') as string;
    const port = this.getInputValue('port') as number;

    // 检查输入值是否有效
    if (!host || !port) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 在浏览器环境中，TCP连接通常通过WebSocket或其他方式模拟
      // 这里创建一个模拟的TCP连接对象
      const connection = {
        host,
        port,
        connected: true,
        send: (data: any) => {
          console.log(`TCP发送到 ${host}:${port}:`, data);
        },
        close: () => {
          console.log(`TCP连接到 ${host}:${port} 已关闭`);
        }
      };

      // 设置输出值
      this.setOutputValue('connection', connection);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      console.error('TCP连接失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册网络协议节点
 * @param registry 节点注册表
 */
export function registerNetworkProtocolNodes(registry: NodeRegistry): void {
  // 注册UDP发送节点
  registry.registerNodeType({
    type: 'network/protocol/udpSend',
    category: NodeCategory.NETWORK,
    constructor: UDPSendNode,
    label: 'UDP发送',
    description: '使用UDP协议发送数据',
    icon: 'udp',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'udp', 'send']
  });

  // 注册HTTP请求节点
  registry.registerNodeType({
    type: 'network/protocol/httpRequest',
    category: NodeCategory.NETWORK,
    constructor: HTTPRequestNode,
    label: 'HTTP请求',
    description: '发送HTTP请求',
    icon: 'http',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'http', 'request']
  });

  // 注册WebSocket连接节点
  registry.registerNodeType({
    type: 'network/protocol/websocketConnect',
    category: NodeCategory.NETWORK,
    constructor: WebSocketConnectNode,
    label: 'WebSocket连接',
    description: '创建WebSocket连接',
    icon: 'websocket',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'websocket', 'connect']
  });

  // 注册TCP连接节点
  registry.registerNodeType({
    type: 'network/protocol/tcpConnect',
    category: NodeCategory.NETWORK,
    constructor: TCPConnectNode,
    label: 'TCP连接',
    description: '创建TCP连接',
    icon: 'tcp',
    color: '#00BCD4',
    tags: ['network', 'protocol', 'tcp', 'connect']
  });
}
